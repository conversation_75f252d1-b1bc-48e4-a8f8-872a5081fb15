'use client'

import { useState, useRef, useEffect } from 'react'
import { useAuth } from '@/components/providers/AuthProvider'
import { 
  PencilIcon, 
  TrashIcon, 
  ArrowRightOnRectangleIcon 
} from '@heroicons/react/24/outline'

interface CommentAvatarPopupProps {
  commentId: string
  commentUserId: string
  isOpen: boolean
  onClose: () => void
  onEdit: () => void
  onDelete: () => void
  avatarRef: React.RefObject<HTMLImageElement>
}

export default function CommentAvatarPopup({
  commentId,
  commentUserId,
  isOpen,
  onClose,
  onEdit,
  onDelete,
  avatarRef
}: CommentAvatarPopupProps) {
  const { user, signOut } = useAuth()
  const popupRef = useRef<HTMLDivElement>(null)
  const [position, setPosition] = useState({ top: 0, left: 0 })

  // Only show popup if user owns the comment
  const canShowPopup = user && user.uid === commentUserId

  useEffect(() => {
    if (isOpen && avatarRef.current && canShowPopup) {
      // Get avatar position relative to its parent container
      const avatar = avatarRef.current
      const avatarRect = avatar.getBoundingClientRect()

      // Find the comment container (the relative positioned parent)
      let commentContainer = avatar.closest('.relative')
      if (!commentContainer) {
        commentContainer = avatar.closest('[class*="relative"]')
      }

      if (commentContainer) {
        const containerRect = commentContainer.getBoundingClientRect()

        // Position relative to the comment container
        setPosition({
          top: avatarRect.bottom - containerRect.top,
          left: avatarRect.left - containerRect.left
        })
      }
    }
  }, [isOpen, avatarRef, canShowPopup])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popupRef.current && !popupRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleEscape)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen, onClose])

  const handleLogout = async () => {
    try {
      await signOut()
      onClose()
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  if (!isOpen || !canShowPopup) {
    return null
  }

  return (
    <div
      ref={popupRef}
      className="absolute z-50 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-1"
      style={{
        top: `${position.top}px`,
        left: `${position.left}px`,
        transform: 'translate(-100%, -2px)', // Move popup left by its full width, up by 2px for slight overlap
      }}
    >
      <button
        onClick={() => {
          onEdit()
          onClose()
        }}
        className="flex items-center w-full px-3 py-2 text-xs text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
      >
        <PencilIcon className="w-3 h-3 mr-2" />
        Edit
      </button>

      <button
        onClick={() => {
          onDelete()
          onClose()
        }}
        className="flex items-center w-full px-3 py-2 text-xs text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
      >
        <TrashIcon className="w-3 h-3 mr-2" />
        Delete
      </button>

      <div className="border-t border-gray-200 dark:border-gray-700 my-1"></div>

      <button
        onClick={handleLogout}
        className="flex items-center w-full px-3 py-2 text-xs text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
      >
        <ArrowRightOnRectangleIcon className="w-3 h-3 mr-2" />
        Logout
      </button>
    </div>
  )
}
